# -*- coding: mbcs -*-
"""
Abaqus脚本：提取SET-RESPON节点集的三向节点力并导出到Excel
基于NFORC字段提取节点力，适用于无位移约束的节点
作者：AI Assistant
日期：2025-08-02
"""

from part import *
from material import *
from section import *
from assembly import *
from step import *
from interaction import *
from load import *
from mesh import *
from optimization import *
from job import *
from sketch import *
from visualization import *
from connectorBehavior import *
import math
import os
import numpy as np
import xyPlot

def extract_nodal_forces_to_excel(odb_path, node_set_name='SET-RESPON', step_name=None, output_file=None):
    """
    从odb文件中提取指定节点集的三向节点力并导出到Excel格式文件
    
    参数:
    odb_path: odb文件路径
    node_set_name: 节点集名称，默认为'SET-RESPON'
    step_name: 分析步名称，如果为None则使用最后一个分析步
    output_file: 输出文件路径，如果为None则自动生成
    """
    
    print("开始提取节点力...")
    print("ODB文件路径: %s" % odb_path)
    print("节点集名称: %s" % node_set_name)
    
    # 检查odb文件是否存在
    if not os.path.exists(odb_path):
        print("错误：找不到odb文件: %s" % odb_path)
        return False
    
    try:
        # 打开odb文件
        print("正在打开odb文件...")
        myodb = openOdb(path=odb_path)
        
        # 获取分析步
        if step_name is None:
            # 使用最后一个分析步
            step_names = myodb.steps.keys()
            if not step_names:
                print("错误：odb文件中没有找到分析步")
                myodb.close()
                return False
            step_name = step_names[-1]
            print("使用分析步: %s" % step_name)
        
        # 获取最后一帧
        lastframe = myodb.steps[step_name].frames[-1]
        print("使用帧: %d" % (len(myodb.steps[step_name].frames) - 1))
        
        # 检查NFORC字段是否存在
        available_fields = lastframe.fieldOutputs.keys()
        print("可用的字段输出: %s" % str(available_fields))
        
        if 'NFORC1' not in available_fields:
            print("错误：在odb文件中未找到NFORC1字段")
            print("请确保在Job设置中启用了NFORC输出")
            myodb.close()
            return False
        
        # 获取三个方向的节点力
        rf1 = lastframe.fieldOutputs['NFORC1']  # X方向节点力
        rf2 = lastframe.fieldOutputs['NFORC2']  # Y方向节点力  
        rf3 = lastframe.fieldOutputs['NFORC3']  # Z方向节点力
        
        # 检查节点集是否存在
        if node_set_name not in myodb.rootAssembly.nodeSets.keys():
            print("错误：未找到节点集 '%s'" % node_set_name)
            print("可用的节点集: %s" % str(myodb.rootAssembly.nodeSets.keys()))
            myodb.close()
            return False
        
        # 获取节点集
        target_node_set = myodb.rootAssembly.nodeSets[node_set_name]
        print("节点集 '%s' 包含 %d 个节点" % (node_set_name, len(target_node_set.nodes)))
        
        # 提取节点力数据
        print("正在提取节点力数据...")
        nodal_forces = []
        
        # 获取节点集中每个节点的力
        for node in target_node_set.nodes:
            node_id = node.label
            
            try:
                # 创建包含单个节点的区域
                single_node_region = myodb.rootAssembly.NodeSet(
                    name='TEMP_NODE_%d' % node_id, 
                    nodes=(node,)
                )
                
                # 提取该节点的三向力
                # 注意：根据CSDN文章，NFORC的方向与实际相反，需要加负号
                force_x_values = rf1.getSubset(region=single_node_region).values
                force_y_values = rf2.getSubset(region=single_node_region).values
                force_z_values = rf3.getSubset(region=single_node_region).values
                
                # 对于每个节点，可能有多个力值（如果节点被多个单元共享）
                # 需要求和得到节点的总力
                force_x = -sum([item.data for item in force_x_values])  # 加负号修正方向
                force_y = -sum([item.data for item in force_y_values])  # 加负号修正方向
                force_z = -sum([item.data for item in force_z_values])  # 加负号修正方向
                
                nodal_forces.append([node_id, force_x, force_y, force_z])
                
            except Exception as e:
                print("警告：提取节点 %d 的力时出错: %s" % (node_id, str(e)))
                continue
        
        # 关闭odb文件
        myodb.close()
        
        if not nodal_forces:
            print("错误：未能提取到任何节点力数据")
            return False
        
        print("成功提取 %d 个节点的力数据" % len(nodal_forces))
        
        # 生成输出文件名
        if output_file is None:
            odb_dir = os.path.dirname(odb_path)
            odb_name = os.path.splitext(os.path.basename(odb_path))[0]
            output_file = os.path.join(odb_dir, '%s_nodal_forces.csv' % odb_name)
        
        # 导出到CSV文件（Excel兼容格式）
        print("正在导出到文件: %s" % output_file)
        
        with open(output_file, 'w') as f:
            # 写入表头
            f.write('Node_ID,Force_X,Force_Y,Force_Z\n')
            
            # 写入数据
            for row in nodal_forces:
                f.write('%d,%.9f,%.9f,%.9f\n' % (row[0], row[1], row[2], row[3]))
        
        print("节点力数据已成功导出到: %s" % output_file)
        print("文件格式：CSV（可用Excel打开）")
        print("数据列：节点ID, X方向力, Y方向力, Z方向力")
        
        return True
        
    except Exception as e:
        print("错误：%s" % str(e))
        try:
            myodb.close()
        except:
            pass
        return False

# 主程序
if __name__ == "__main__":
    # 设置参数
    # 请根据实际情况修改以下参数
    
    # ODB文件路径 - 请修改为您的实际路径
    odb_file_path = "Job-1.odb"  # 修改为您的odb文件路径
    
    # 节点集名称
    node_set_name = "SET-RESPON"
    
    # 分析步名称（可选，如果为None则使用最后一个分析步）
    step_name = None  # 例如: "Step-1"
    
    # 输出文件路径（可选，如果为None则自动生成）
    output_file_path = None  # 例如: "nodal_forces_output.csv"
    
    # 执行提取
    success = extract_nodal_forces_to_excel(
        odb_path=odb_file_path,
        node_set_name=node_set_name,
        step_name=step_name,
        output_file=output_file_path
    )
    
    if success:
        print("\n=== 提取完成 ===")
        print("节点力提取成功！")
    else:
        print("\n=== 提取失败 ===")
        print("请检查错误信息并修正参数")
