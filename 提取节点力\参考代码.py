#展示了目前绘制力的图线的操作
odb = session.odbs['C:/SIMULIA/EstProducts/2024/win_b64/resources/install/cmdDirFeature/Job-4.odb']
xy1 = xyPlot.XYDataFromHistory(odb=odb, 
    outputVariableName='Spatial acceleration: A1 at Node 108700 in NSET SET-RESPON', 
    steps=('Step-2', ), suppressQuery=True, __linkedVpName__='Viewport: 1')
c1 = session.Curve(xyData=xy1)
xyp = session.XYPlot('XYPlot-1')
chartName = xyp.charts.keys()[0]
chart = xyp.charts[chartName]
chart.setValues(curvesToPlot=(c1, ), )
session.charts[chartName].autoColor(lines=True, symbols=True)
session.viewports['Viewport: 1'].setValues(displayedObject=xyp)
session.xyPlots[session.viewports['Viewport: 1'].displayedObject.name].setValues(
    transform=(0.862069, 0, 0, -0.00551433, 0, 0.862069, 0, -0.00217608, 0, 0, 
    0.862069, 0, 0, 0, 0, 1))
session.charts['Chart-1'].legend.area.setValues(positionMethod=MANUAL, 
    originOffset=(0.678905, 0.0106727))
