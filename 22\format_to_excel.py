# -*- coding: utf-8 -*-
"""
将CSV格式的节点力数据转换为格式化的Excel文件
此脚本可以在普通Python环境中运行（不需要Abaqus）
需要安装：pip install pandas openpyxl
"""

import pandas as pd
import os
import sys

def format_csv_to_excel(csv_file, excel_file=None):
    """
    将CSV格式的节点力数据转换为格式化的Excel文件
    
    参数:
    csv_file: 输入的CSV文件路径
    excel_file: 输出的Excel文件路径，如果为None则自动生成
    """
    
    if not os.path.exists(csv_file):
        print("错误：找不到CSV文件: %s" % csv_file)
        return False
    
    try:
        # 读取CSV文件
        print("正在读取CSV文件: %s" % csv_file)
        df = pd.read_csv(csv_file)
        
        # 检查数据格式
        expected_columns = ['Node_ID', 'Force_X', 'Force_Y', 'Force_Z']
        if list(df.columns) != expected_columns:
            print("警告：CSV文件列名不匹配")
            print("期望的列名: %s" % expected_columns)
            print("实际的列名: %s" % list(df.columns))
        
        # 生成Excel文件名
        if excel_file is None:
            base_name = os.path.splitext(csv_file)[0]
            excel_file = base_name + '_formatted.xlsx'
        
        print("正在创建Excel文件: %s" % excel_file)
        
        # 创建Excel写入器
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 写入主数据表
            df.to_excel(writer, sheet_name='节点力数据', index=False)
            
            # 获取工作表对象
            worksheet = writer.sheets['节点力数据']
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 12  # Node_ID
            worksheet.column_dimensions['B'].width = 15  # Force_X
            worksheet.column_dimensions['C'].width = 15  # Force_Y
            worksheet.column_dimensions['D'].width = 15  # Force_Z
            
            # 创建统计信息表
            stats_data = {
                '统计项目': [
                    '节点总数',
                    'X方向力最大值',
                    'X方向力最小值',
                    'X方向力平均值',
                    'Y方向力最大值',
                    'Y方向力最小值', 
                    'Y方向力平均值',
                    'Z方向力最大值',
                    'Z方向力最小值',
                    'Z方向力平均值',
                    'X方向力合力',
                    'Y方向力合力',
                    'Z方向力合力'
                ],
                '数值': [
                    len(df),
                    df['Force_X'].max(),
                    df['Force_X'].min(),
                    df['Force_X'].mean(),
                    df['Force_Y'].max(),
                    df['Force_Y'].min(),
                    df['Force_Y'].mean(),
                    df['Force_Z'].max(),
                    df['Force_Z'].min(),
                    df['Force_Z'].mean(),
                    df['Force_X'].sum(),
                    df['Force_Y'].sum(),
                    df['Force_Z'].sum()
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            # 设置统计信息表的列宽
            stats_worksheet = writer.sheets['统计信息']
            stats_worksheet.column_dimensions['A'].width = 20
            stats_worksheet.column_dimensions['B'].width = 20
        
        print("Excel文件创建成功！")
        print("文件包含两个工作表：")
        print("  1. 节点力数据 - 完整的节点力数据")
        print("  2. 统计信息 - 数据统计摘要")
        
        # 显示统计信息
        print("\n=== 数据统计摘要 ===")
        print("节点总数: %d" % len(df))
        print("X方向力合力: %.6f" % df['Force_X'].sum())
        print("Y方向力合力: %.6f" % df['Force_Y'].sum())
        print("Z方向力合力: %.6f" % df['Force_Z'].sum())
        
        return True
        
    except Exception as e:
        print("错误：%s" % str(e))
        return False

def main():
    """主函数"""
    print("=== CSV到Excel转换工具 ===")
    
    # 查找CSV文件
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv') and 'nodal_forces' in f]
    
    if not csv_files:
        print("在当前目录中未找到节点力CSV文件")
        print("请确保已运行Abaqus提取脚本并生成了CSV文件")
        return
    
    if len(csv_files) == 1:
        csv_file = csv_files[0]
        print("找到CSV文件: %s" % csv_file)
    else:
        print("找到多个CSV文件:")
        for i, f in enumerate(csv_files):
            print("  %d. %s" % (i+1, f))
        
        try:
            choice = int(input("请选择要转换的文件编号: ")) - 1
            if 0 <= choice < len(csv_files):
                csv_file = csv_files[choice]
            else:
                print("无效的选择")
                return
        except ValueError:
            print("无效的输入")
            return
    
    # 执行转换
    success = format_csv_to_excel(csv_file)
    
    if success:
        print("\n转换完成！")
    else:
        print("\n转换失败！")

if __name__ == "__main__":
    try:
        import pandas as pd
        import openpyxl
    except ImportError:
        print("错误：缺少必要的Python库")
        print("请安装以下库：")
        print("  pip install pandas openpyxl")
        sys.exit(1)
    
    main()
    input("\n按回车键退出...")
