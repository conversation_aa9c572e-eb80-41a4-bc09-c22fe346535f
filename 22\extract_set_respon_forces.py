# -*- coding: mbcs -*-
"""
简化版本：专门提取SET-RESPON节点集的三向节点力
基于参考代码优化，直接输出Excel兼容的CSV格式
"""

from part import *
from material import *
from section import *
from assembly import *
from step import *
from interaction import *
from load import *
from mesh import *
from optimization import *
from job import *
from sketch import *
from visualization import *
from connectorBehavior import *
import math
import os
import numpy as np

# =============================================================================
# 主要参数设置区域 - 请根据您的实际情况修改
# =============================================================================

# ODB文件路径 - 请修改为您的实际odb文件路径
odb_path = 'C:/SIMULIA/EstProducts/2024/win_b64/resources/install/cmdDirFeature/Job-4.odb'  # 修改这里！

# 节点集名称
node_set_name = 'SET-RESPON'

# 分析步名称（通常是'Step-1'，如果不确定可以设为None自动选择最后一步）
step_name = 'Step-2'  # 或者设为None

# 输出文件名
output_filename = 'SET_RESPON_nodal_forces.csv'

# =============================================================================
# 主程序
# =============================================================================

print("=== Abaqus节点力提取脚本 ===")
print("目标节点集: %s" % node_set_name)
print("ODB文件: %s" % odb_path)

try:
    # 打开odb文件
    print("\n正在打开odb文件...")
    myodb = openOdb(path=odb_path)
    
    # 获取分析步
    if step_name is None:
        step_name = myodb.steps.keys()[-1]
    print("使用分析步: %s" % step_name)
    
    # 获取最后一帧的数据
    lastframe = myodb.steps[step_name].frames[-1]
    print("使用最后一帧数据")
    
    # 获取三个方向的节点力场输出
    print("\n正在获取NFORC字段...")
    rf1 = lastframe.fieldOutputs['NFORC1']  # X方向
    rf2 = lastframe.fieldOutputs['NFORC2']  # Y方向
    rf3 = lastframe.fieldOutputs['NFORC3']  # Z方向
    
    # 获取目标节点集
    print("正在获取节点集: %s" % node_set_name)
    target_region = myodb.rootAssembly.nodeSets[node_set_name]
    
    # 提取节点集的力数据
    print("正在提取节点力数据...")
    
    # 获取节点集区域的力值
    setrf1_values = rf1.getSubset(region=target_region).values
    setrf2_values = rf2.getSubset(region=target_region).values  
    setrf3_values = rf3.getSubset(region=target_region).values
    
    # 准备存储结果的列表
    nodal_forces_data = []
    
    # 处理每个节点的力数据
    print("正在处理节点数据...")
    
    # 创建节点ID到力值的映射
    node_forces = {}
    
    # 处理X方向力
    for value in setrf1_values:
        node_id = value.nodeLabel
        force_x = -value.data  # 注意：加负号修正NFORC方向
        if node_id not in node_forces:
            node_forces[node_id] = [0.0, 0.0, 0.0]  # [Fx, Fy, Fz]
        node_forces[node_id][0] += force_x
    
    # 处理Y方向力
    for value in setrf2_values:
        node_id = value.nodeLabel
        force_y = -value.data  # 注意：加负号修正NFORC方向
        if node_id not in node_forces:
            node_forces[node_id] = [0.0, 0.0, 0.0]
        node_forces[node_id][1] += force_y
    
    # 处理Z方向力
    for value in setrf3_values:
        node_id = value.nodeLabel
        force_z = -value.data  # 注意：加负号修正NFORC方向
        if node_id not in node_forces:
            node_forces[node_id] = [0.0, 0.0, 0.0]
        node_forces[node_id][2] += force_z
    
    # 转换为列表格式并按节点ID排序
    for node_id in sorted(node_forces.keys()):
        forces = node_forces[node_id]
        nodal_forces_data.append([node_id, forces[0], forces[1], forces[2]])
    
    print("成功处理 %d 个节点的力数据" % len(nodal_forces_data))
    
    # 关闭odb文件
    myodb.close()
    
    # 导出到CSV文件
    print("\n正在导出数据到文件: %s" % output_filename)
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_path = os.path.join(script_dir, output_filename)
    
    with open(output_path, 'w') as f:
        # 写入表头
        f.write('Node_ID,Force_X,Force_Y,Force_Z\n')
        
        # 写入数据
        for row in nodal_forces_data:
            f.write('%d,%.9f,%.9f,%.9f\n' % (row[0], row[1], row[2], row[3]))
    
    print("=== 提取完成 ===")
    print("输出文件: %s" % output_path)
    print("数据格式: CSV (可用Excel打开)")
    print("数据说明:")
    print("  - Node_ID: Abaqus节点编号")
    print("  - Force_X: X方向节点力 (已修正NFORC方向)")
    print("  - Force_Y: Y方向节点力 (已修正NFORC方向)")
    print("  - Force_Z: Z方向节点力 (已修正NFORC方向)")
    print("  - 总共 %d 个节点" % len(nodal_forces_data))
    
    # 显示前几行数据作为预览
    print("\n数据预览 (前5行):")
    print("Node_ID\tForce_X\t\tForce_Y\t\tForce_Z")
    print("-" * 60)
    for i, row in enumerate(nodal_forces_data[:5]):
        print("%d\t%.6f\t%.6f\t%.6f" % (row[0], row[1], row[2], row[3]))
    
    if len(nodal_forces_data) > 5:
        print("... (还有 %d 行数据)" % (len(nodal_forces_data) - 5))

except Exception as e:
    print("错误: %s" % str(e))
    print("\n请检查以下事项:")
    print("1. ODB文件路径是否正确")
    print("2. 节点集名称是否正确")
    print("3. 是否在Job设置中启用了NFORC输出")
    print("4. 分析步名称是否正确")
    
    # 尝试关闭odb文件
    try:
        myodb.close()
    except:
        pass

print("\n脚本执行完毕")
