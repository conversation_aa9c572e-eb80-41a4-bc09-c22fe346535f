@echo off
echo ========================================
echo Abaqus节点力提取脚本运行器
echo ========================================
echo.

REM 设置Abaqus安装路径（请根据实际情况修改）
set ABAQUS_PATH="C:\SIMULIA\EstProducts\2024\win_b64\code\bin\abaqus.bat"

REM 检查Abaqus是否存在
if not exist %ABAQUS_PATH% (
    echo 错误：找不到Abaqus安装路径
    echo 请修改此批处理文件中的ABAQUS_PATH变量
    echo 当前设置：%ABAQUS_PATH%
    pause
    exit /b 1
)

echo 找到Abaqus安装路径：%ABAQUS_PATH%
echo.

REM 检查Python脚本是否存在
if not exist "extract_set_respon_forces.py" (
    echo 错误：找不到Python脚本文件
    echo 请确保extract_set_respon_forces.py在当前目录中
    pause
    exit /b 1
)

echo 找到Python脚本：extract_set_respon_forces.py
echo.

echo 正在运行Abaqus Python脚本...
echo 请等待...
echo.

REM 运行Abaqus Python脚本
%ABAQUS_PATH% cae noGUI=extract_set_respon_forces.py

echo.
echo ========================================
echo 脚本执行完成
echo ========================================
echo.

REM 检查是否生成了输出文件
if exist "SET_RESPON_nodal_forces.csv" (
    echo 成功生成输出文件：SET_RESPON_nodal_forces.csv
    echo 您可以用Excel打开此文件查看结果
) else (
    echo 警告：未找到输出文件，请检查脚本执行过程中的错误信息
)

echo.
pause
