最后处理力的方向问题
data=table2array(readtable('.\test\result1.txt'));
%第一列为set的编号，后三列对应的Fx、Fy、Fz
pfaceXplus;%BB1C1C面上的所有集合编号，包含点线面
pfaceYplus;%ABCD面上的所有集合编号，包含点线面
pfaceZplus;%AA1B1B面上的所有集合编号，包含点线面
ylength = 1; % 你需要的值
xlength = 1; % 你需要的值
zlength = 1; % 你需要的值

% 查找索引列表对应的行并提取对应的行
[is_found, idx] = ismember(pfaceXplus, data(:, 1));
forcefaceXplus = data(idx(is_found), :);
forcefaceXplus=forcefaceXplus(:,2:4);
[is_found, idx] = ismember(pfaceYplus, data(:, 1));
forcefaceYplus = data(idx(is_found), :);
forcefaceYplus=forcefaceYplus(:,2:4);
[is_found, idx] = ismember(pfaceZplus, data(:, 1));
forcefaceZplus = data(idx(is_found), :);
forcefaceZplus=forcefaceZplus(:,2:4);
%加负号的原因是nforc的结果和free body的方向相反
sigma11=-sum(forcefaceXplus(:,1))/ylength/zlength;
sigma12=-sum(forcefaceXplus(:,2))/ylength/zlength;
sigma13=-sum(forcefaceXplus(:,3))/ylength/zlength;
sigma21=-sum(forcefaceYplus(:,1))/xlength/zlength;
sigma22=-sum(forcefaceYplus(:,2))/xlength/zlength;
sigma23=-sum(forcefaceYplus(:,3))/xlength/zlength;
sigma31=-sum(forcefaceZplus(:,1))/xlength/ylength;
sigma32=-sum(forcefaceZplus(:,2))/xlength/ylength;
sigma33=-sum(forcefaceZplus(:,3))/xlength/ylength;